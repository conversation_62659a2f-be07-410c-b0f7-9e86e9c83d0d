<?php
// --- Datenbankverbindung und Logik --- //
$db = new PDO('sqlite:todo.db');
$db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Tabelle anlegen
$db->exec("CREATE TABLE IF NOT EXISTS todos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    text TEXT NOT NULL,
    erstellt_am DATETIME DEFAULT CURRENT_TIMESTAMP
)");

// Aufgabe hinzufügen
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['text'])) {
    $text = trim($_POST['text']);
    if ($text !== '') {
        $stmt = $db->prepare("INSERT INTO todos (text) VALUES (:text)");
        $stmt->execute([':text' => $text]);
    }
    header("Location: " . $_SERVER['PHP_SELF']); // vermeidet <PERSON>
    exit;
}

// Aufgabe löschen
if (isset($_GET['delete'])) {
    $id = (int) $_GET['delete'];
    $stmt = $db->prepare("DELETE FROM todos WHERE id = :id");
    $stmt->execute([':id' => $id]);
    header("Location: " . $_SERVER['PHP_SELF']);
    exit;
}

// Aufgaben abrufen
$todos = $db->query("SELECT * FROM todos ORDER BY erstellt_am DESC")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>To-Do-Liste mit SQLite</title>
</head>
<body>
    <h1>Meine To-Do-Liste 📝</h1>

    <form method="post">
        <input type="text" name="text" required placeholder="Neue Aufgabe">
        <button type="submit">Hinzufügen</button>
    </form>

    <ul>
        <?php foreach ($todos as $todo): ?>
            <li>
                <?= htmlspecialchars($todo['text']) ?>
                <a href="?delete=<?= $todo['id'] ?>" onclick="return confirm('Wirklich löschen?')">🗑️</a>
            </li>
        <?php endforeach; ?>
    </ul>
</body>
</html>
